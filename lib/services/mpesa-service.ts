import axios from 'axios';
import { validatePhoneNumber, validateAmount, mpesaRateLimiter } from '@/lib/utils/validation';

export interface MpesaPaymentRequest {
  phoneNumber: string;
  amount: number;
  accountReference?: string;
}

export interface MpesaPaymentResponse {
  success: boolean;
  message: string;
  data?: {
    MerchantRequestID: string;
    CheckoutRequestID: string;
    ResponseCode: string;
    ResponseDescription: string;
    CustomerMessage: string;
  };
  error?: string;
}

export interface PaymentStatus {
  checkoutRequestId: string;
  merchantRequestId: string;
  status: 'pending' | 'completed' | 'failed' | 'expired';
  amount?: number;
  mpesaReceiptNumber?: string;
  transactionDate?: string;
  phoneNumber?: string;
  resultDesc?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TokenPurchaseRequest {
  paymentMethod: 'mpesa';
  amount: number;
  phoneNumber: string;
  mpesaReceiptNumber: string;
  checkoutRequestId: string;
  merchantRequestId: string;
}

export interface TokenPurchaseResponse {
  success: boolean;
  message: string;
  data?: {
    usdAmount: number;
    tokenAmount: number;
    tokenPrice: number;
    paymentMethod: string;
    transactionId: string;
    timestamp: string;
  };
  error?: string;
}

class MpesaService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  }

  /**
   * Initiate M-Pesa STK Push payment
   */
  async initiatePayment(request: MpesaPaymentRequest): Promise<MpesaPaymentResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/mpesa/payment/initiate`, request);
      return response.data;
    } catch (error: any) {
      console.error('M-Pesa payment initiation error:', error);
      return {
        success: false,
        message: 'Failed to initiate payment',
        error: error.response?.data?.error || error.message,
      };
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(checkoutRequestId: string): Promise<PaymentStatus | null> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/mpesa/payment/status?checkoutRequestId=${checkoutRequestId}`
      );
      
      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (error: any) {
      console.error('Payment status check error:', error);
      return null;
    }
  }

  /**
   * Poll payment status with timeout
   */
  async pollPaymentStatus(
    checkoutRequestId: string,
    timeoutMs: number = 120000, // 2 minutes
    intervalMs: number = 3000 // 3 seconds
  ): Promise<PaymentStatus | null> {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      const poll = async () => {
        try {
          const status = await this.checkPaymentStatus(checkoutRequestId);
          
          if (status && status.status !== 'pending') {
            resolve(status);
            return;
          }
          
          if (Date.now() - startTime >= timeoutMs) {
            resolve(null); // Timeout
            return;
          }
          
          setTimeout(poll, intervalMs);
        } catch (error) {
          console.error('Polling error:', error);
          setTimeout(poll, intervalMs);
        }
      };
      
      poll();
    });
  }

  /**
   * Complete token purchase after successful M-Pesa payment
   */
  async completeTokenPurchase(request: TokenPurchaseRequest): Promise<TokenPurchaseResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/tokens/purchase`, request);
      return response.data;
    } catch (error: any) {
      console.error('Token purchase completion error:', error);
      return {
        success: false,
        message: 'Failed to complete token purchase',
        error: error.response?.data?.error || error.message,
      };
    }
  }

  /**
   * Validate phone number format
   */
  validatePhoneNumber(phoneNumber: string): boolean {
    // Remove any spaces, dashes, or plus signs
    const cleaned = phoneNumber.replace(/[\s\-\+]/g, '');
    
    // Check if it matches Kenyan format (254XXXXXXXXX)
    const kenyaRegex = /^254[0-9]{9}$/;
    
    // If it starts with 0, convert to 254 format
    if (cleaned.startsWith('0') && cleaned.length === 10) {
      return true; // Will be converted in formatPhoneNumber
    }
    
    return kenyaRegex.test(cleaned);
  }

  /**
   * Format phone number to M-Pesa format (254XXXXXXXXX)
   */
  formatPhoneNumber(phoneNumber: string): string {
    // Remove any spaces, dashes, or plus signs
    const cleaned = phoneNumber.replace(/[\s\-\+]/g, '');
    
    // If it starts with 0, replace with 254
    if (cleaned.startsWith('0') && cleaned.length === 10) {
      return '254' + cleaned.substring(1);
    }
    
    // If it already starts with 254, return as is
    if (cleaned.startsWith('254') && cleaned.length === 12) {
      return cleaned;
    }
    
    // If it starts with 7 and is 9 digits, add 254
    if (cleaned.startsWith('7') && cleaned.length === 9) {
      return '254' + cleaned;
    }
    
    return cleaned;
  }

  /**
   * Calculate token amount based on USD amount
   */
  calculateTokenAmount(usdAmount: number, tokenPrice: number = 0.0245): number {
    return usdAmount / tokenPrice;
  }

  /**
   * Format currency for display
   */
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Format token amount for display
   */
  formatTokenAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  }
}

export const mpesaService = new MpesaService();
