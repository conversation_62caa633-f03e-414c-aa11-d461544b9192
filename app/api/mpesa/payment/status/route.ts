import { NextRequest, NextResponse } from 'next/server';

// Payment status endpoint for frontend polling
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const checkoutRequestId = searchParams.get('checkoutRequestId');
    const merchantRequestId = searchParams.get('merchantRequestId');

    if (!checkoutRequestId && !merchantRequestId) {
      return NextResponse.json(
        { error: 'CheckoutRequestID or MerchantRequestID is required' },
        { status: 400 }
      );
    }

    // TODO: Implement database lookup for payment status
    // For now, we'll return a placeholder response
    // In a real implementation, you would:
    // const paymentStatus = await getPaymentStatus(checkoutRequestId || merchantRequestId);

    // Placeholder response - replace with actual database lookup
    const mockPaymentStatus = {
      checkoutRequestId,
      merchantRequestId,
      status: 'pending', // pending, completed, failed, expired
      amount: null,
      mpesaReceiptNumber: null,
      transactionDate: null,
      phoneNumber: null,
      resultDesc: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: mockPaymentStatus,
    });

  } catch (error: any) {
    console.error('Payment status check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check payment status',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Update payment status (for internal use)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { checkoutRequestId, merchantRequestId, status, ...updateData } = body;

    if (!checkoutRequestId && !merchantRequestId) {
      return NextResponse.json(
        { error: 'CheckoutRequestID or MerchantRequestID is required' },
        { status: 400 }
      );
    }

    // TODO: Implement database update for payment status
    // const updatedPayment = await updatePaymentStatus({
    //   checkoutRequestId,
    //   merchantRequestId,
    //   status,
    //   ...updateData
    // });

    return NextResponse.json({
      success: true,
      message: 'Payment status updated successfully',
      // data: updatedPayment,
    });

  } catch (error: any) {
    console.error('Payment status update error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update payment status',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
