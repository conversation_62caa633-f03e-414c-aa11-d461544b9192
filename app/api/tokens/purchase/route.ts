import { NextRequest, NextResponse } from 'next/server';

// Token purchase endpoint for M-Pesa payments
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      paymentMethod, 
      amount, 
      phoneNumber, 
      mpesaReceiptNumber, 
      checkoutRequestId,
      merchantRequestId 
    } = body;

    // Validation
    if (!paymentMethod || !amount) {
      return NextResponse.json(
        { error: 'Payment method and amount are required' },
        { status: 400 }
      );
    }

    if (paymentMethod === 'mpesa') {
      if (!phoneNumber || !mpesaReceiptNumber) {
        return NextResponse.json(
          { error: 'Phone number and M-Pesa receipt number are required for M-Pesa payments' },
          { status: 400 }
        );
      }
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Get token configuration
    const TOKEN_PRICE = parseFloat(process.env.NOMA_TOKEN_PRICE || '0.0245'); // $0.0245 per token
    const MIN_PURCHASE = parseFloat(process.env.MIN_PURCHASE_AMOUNT || '10'); // $10 minimum
    const MAX_PURCHASE = parseFloat(process.env.MAX_PURCHASE_AMOUNT || '10000'); // $10,000 maximum

    // Validate purchase limits
    if (numericAmount < MIN_PURCHASE) {
      return NextResponse.json(
        { error: `Minimum purchase amount is $${MIN_PURCHASE}` },
        { status: 400 }
      );
    }

    if (numericAmount > MAX_PURCHASE) {
      return NextResponse.json(
        { error: `Maximum purchase amount is $${MAX_PURCHASE}` },
        { status: 400 }
      );
    }

    // Calculate token amount
    const tokenAmount = numericAmount / TOKEN_PRICE;

    // Create purchase record
    const purchaseData = {
      paymentMethod,
      usdAmount: numericAmount,
      tokenAmount,
      tokenPrice: TOKEN_PRICE,
      phoneNumber: paymentMethod === 'mpesa' ? phoneNumber : null,
      mpesaReceiptNumber: paymentMethod === 'mpesa' ? mpesaReceiptNumber : null,
      checkoutRequestId: paymentMethod === 'mpesa' ? checkoutRequestId : null,
      merchantRequestId: paymentMethod === 'mpesa' ? merchantRequestId : null,
      status: 'completed',
      timestamp: new Date().toISOString(),
    };

    console.log('Token purchase processed:', purchaseData);

    // TODO: Implement the following:
    // 1. Store purchase record in database
    // 2. Update user's token balance
    // 3. Send confirmation email
    // 4. Trigger any post-purchase webhooks
    // 5. Update analytics/metrics

    // For now, we'll just return the purchase data
    // In a real implementation, you would:
    // const purchase = await createTokenPurchase(purchaseData);
    // await updateUserBalance(userAddress, tokenAmount);
    // await sendPurchaseConfirmation(purchaseData);

    return NextResponse.json({
      success: true,
      message: 'Token purchase completed successfully',
      data: {
        usdAmount: numericAmount,
        tokenAmount,
        tokenPrice: TOKEN_PRICE,
        paymentMethod,
        transactionId: paymentMethod === 'mpesa' ? mpesaReceiptNumber : null,
        timestamp: purchaseData.timestamp,
      },
    });

  } catch (error: any) {
    console.error('Token purchase error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process token purchase',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Get purchase history (for user dashboard)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userAddress = searchParams.get('userAddress');
    const phoneNumber = searchParams.get('phoneNumber');

    if (!userAddress && !phoneNumber) {
      return NextResponse.json(
        { error: 'User address or phone number is required' },
        { status: 400 }
      );
    }

    // TODO: Implement database lookup for purchase history
    // const purchases = await getUserPurchases(userAddress || phoneNumber);

    // Placeholder response
    const mockPurchases = [
      {
        id: '1',
        paymentMethod: 'mpesa',
        usdAmount: 100,
        tokenAmount: 4081.63,
        tokenPrice: 0.0245,
        status: 'completed',
        timestamp: new Date().toISOString(),
        transactionId: 'NLJ7RT61SV',
      },
    ];

    return NextResponse.json({
      success: true,
      data: mockPurchases,
    });

  } catch (error: any) {
    console.error('Purchase history error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch purchase history',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
