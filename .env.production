# NomaToken DApp - Production Environment Variables

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NEXT_PUBLIC_SITE_NAME="NomaToken"
NEXT_PUBLIC_SITE_DESCRIPTION="Professional cryptocurrency investment platform"

# Web3 Configuration
NEXT_PUBLIC_WEB3_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_INFURA_PROJECT_ID=your_infura_project_id
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key

# Blockchain Networks
NEXT_PUBLIC_CHAIN_ID=56
NEXT_PUBLIC_NETWORK_NAME="BSC Mainnet"
NEXT_PUBLIC_RPC_URL=https://bsc-dataseed.binance.org/

# Contract Addresses (Update with your deployed contracts)
NEXT_PUBLIC_TOKEN_CONTRACT=0x...
NEXT_PUBLIC_STAKING_CONTRACT=0x...
NEXT_PUBLIC_PRESALE_CONTRACT=0x...

# API Configuration
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_COINGECKO_API=https://api.coingecko.com/api/v3

# Analytics (Optional)
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANIMATIONS=true
NEXT_PUBLIC_ENABLE_WALLET_CONNECT=true
NEXT_PUBLIC_ENABLE_STAKING=true
NEXT_PUBLIC_ENABLE_PRESALE=true

# Security
NEXT_PUBLIC_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Performance
NEXT_PUBLIC_CDN_URL=https://cdn.yourdomain.com
NEXT_PUBLIC_IMAGE_OPTIMIZATION=false

# Social Media
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/nomatoken
NEXT_PUBLIC_TELEGRAM_URL=https://t.me/nomatoken
NEXT_PUBLIC_DISCORD_URL=https://discord.gg/nomatoken

# Contact
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>

# M-Pesa Configuration
MPESA_ENVIRONMENT=production
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_business_short_code
MPESA_PASSKEY=your_mpesa_passkey
MPESA_CALLBACK_URL=https://yourdomain.com/api/mpesa/payment/callback

# Token Configuration
NOMA_TOKEN_PRICE=0.0245
MIN_PURCHASE_AMOUNT=10
MAX_PURCHASE_AMOUNT=10000

# Environment
NODE_ENV=production
