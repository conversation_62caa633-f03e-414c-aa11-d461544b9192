# NomaToken DApp - Local Development Environment Variables
# Copy this file to .env.local and update with your actual values

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME="NomaToken"
NEXT_PUBLIC_SITE_DESCRIPTION="Professional cryptocurrency investment platform"

# Web3 Configuration
NEXT_PUBLIC_WEB3_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_INFURA_PROJECT_ID=your_infura_project_id
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key
NEXT_PUBLIC_REOWN_PROJECT_ID=your_reown_project_id

# Blockchain Networks
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_NETWORK_NAME="BSC Testnet"
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_ENABLE_TESTNET=true

# Contract Addresses (Testnet)
NEXT_PUBLIC_TOKEN_CONTRACT=0x...
NEXT_PUBLIC_STAKING_CONTRACT=0x...
NEXT_PUBLIC_PRESALE_CONTRACT=0x...

# M-Pesa Configuration (Sandbox)
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=your_sandbox_consumer_key
MPESA_CONSUMER_SECRET=your_sandbox_consumer_secret
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=your_sandbox_passkey
MPESA_CALLBACK_URL=http://localhost:3000/api/mpesa/payment/callback

# Token Configuration
NOMA_TOKEN_PRICE=0.0245
MIN_PURCHASE_AMOUNT=1
MAX_PURCHASE_AMOUNT=1000

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_COINGECKO_API=https://api.coingecko.com/api/v3

# Feature Flags
NEXT_PUBLIC_ENABLE_ANIMATIONS=true
NEXT_PUBLIC_ENABLE_WALLET_CONNECT=true
NEXT_PUBLIC_ENABLE_STAKING=true
NEXT_PUBLIC_ENABLE_PRESALE=true

# Environment
NODE_ENV=development
